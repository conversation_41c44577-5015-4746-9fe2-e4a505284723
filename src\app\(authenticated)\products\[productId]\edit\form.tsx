"use client";

import "react-quill/dist/quill.snow.css";
import Select from "react-select";
import AsyncSelect from "react-select/async";
import AsyncCreatableSelect from "react-select/async-creatable";
import ReactQuill from "react-quill";
import Datepicker from "react-tailwindcss-datepicker";
import { Controller, useForm } from "react-hook-form";
import { useState } from "react";
import toast from "react-hot-toast";
import { imagePath } from "@/lib/api";
import ProductPreview from "@/components/product-preview";
import Image from "next/image";
import CustomDatePicker from "@/components/date-picker";
import ProductModifiers from "@/components/forms/product-modifiers";
import PackagingOptionSelector from "@/components/forms/packaging-option-selector";
import FulfillmentSettings from "@/components/forms/fulfillment-settings";
import { archetypeMap, relevantSettings } from "@/types/fulfillment-settings";
import { PackagingOption } from "@/actions/packaging-options";
import { Modifier } from "@/types/modifiers";
import { Product, Branch, Vendor } from "@/types";

export default function ProductForm({
  userDetails,
  defaultValues,
  branch,
  storeProduct,
  storeTag,
  loadVendors,
  loadBranches,
  loadPackagingOptions,
  loadModifiers,
}: {
  userDetails: User,
  defaultValues: Product;
  branch?: Branch;
  storeProduct: (data: FormData) => Promise<void>;
  storeTag: (data: FormData) => Promise<Tag | undefined>;
  loadVendors: (s: string) => Promise<Vendor[]>;
  loadBranches: (s: string, vendorId: string) => Promise<Branch[]>;
  loadPackagingOptions: () => Promise<PackagingOption[]>;
  loadModifiers: () => Promise<Modifier[]>;
}) {
  const steps: Record<string, string> = {
    details: "Details",
    fulfillment: "Fulfillment",
    images: "Images",
    extra: "Extra",
    review: "Review & Update",
  };

  const [step, setStep] = useState("details");
  const [imagePreview, setImagePreview] = useState<string | null>(
    imagePath(defaultValues.image?.url),
  );
  const [galleryPreview, setGalleryPreview] = useState<string[]>([]);

  const [isLoadingTags, setIsLoadingTags] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    control,
  } = useForm<ProductPayload>({
    defaultValues: {
      ...defaultValues,
      tagIds: defaultValues.tags.map((tag) => tag.id),
      accompaniments: defaultValues.accompaniments.reduce(
        (acc, item) => ({
          ...acc,
          [item.id]: {
            id: item.id,
            price: item.price,
            name: item.name,
          },
        }),
        {},
      ),
      upsells: defaultValues.upsells.reduce(
        (acc, item) => ({
          ...acc,
          [item.id]: {
            id: item.id,
            price: item.price,
            name: item.name,
          },
        }),
        {},
      ),
      fulfillmentSettings: {},
      packagingOptionIds: defaultValues.packagingOptions?.map((option) => option.id) || [],
    },
  });

  const product = watch();

  const loadTags = async (s: string) => {
    if (s.length < 3) return [];

    const response: Tag[] = await fetch(`/api/tags?s=${s}`).then((res) =>
      res.json(),
    );

    return response;
  };

  const loadProducts = async (s: string) => {
    if (s.length < 3) return [];

    const response: Product[] = await fetch(`/api/products?s=${s}`).then(
      (res) => res.json(),
    );

    return response;
  };

  const createTag = async (name: string) => {
    setIsLoadingTags(true);
    const data = new FormData();
    data.append("name", name);

    const res = await storeTag(data);

    if (res) {
      setValue("tagIds", [...product.tagIds, res.id]);
    }

    setIsLoadingTags(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const reader = new FileReader();

      setValue("imageUpload", file);

      reader.onload = () => {
        setImagePreview(reader.result as string);
      };

      reader.readAsDataURL(file);
    }
  };

  const handleGalleryUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (files) {
      Array.from(files).forEach((file: File) => {
        setGalleryPreview([...galleryPreview, URL.createObjectURL(file)]);
      });
    }
  };

  const loadVendorOptions = async (s: string) => {
    if (s.length > 3) {
      const vendors: Vendor[] = await loadVendors(s);

      return vendors?.map((vendor) => ({
        value: vendor.id,
        label: vendor.name,
      }));
    }

    return [];
  };

  const loadBranchOptions = async (s: string, vendorId: string) => {
    if (s.length > 3) {
      const branches: Branch[] = await loadBranches(s, vendorId);

      return branches?.map((branch) => ({
        value: branch.id,
        label: branch.name,
      }));
    }

    return [];
  };

  const onSubmit = (payload: ProductPayload) => {
    const data = new FormData();

    data.append("name", payload.name);
    data.append("ref", payload.ref);
    data.append("details", payload.details);
    data.append("price", payload.price?.toString());
    data.append("discounted", payload.discounted?.toString() || "");
    data.append("stock", payload.stock.toString());
    data.append("active", payload.active.toString());
    data.append("featured", payload.featured.toString());
    data.append("type", payload.type);
    data.append("condition", payload.condition);
    data.append("status", payload.status);
    data.append("availability", payload.availability);
    data.append("shipping", payload.shipping);
    data.append("mode", payload.mode);
    data.append("payment", payload.payment);
    data.append("visibility", payload.visibility);
    const vendorId = Array.isArray(payload.vendorId) ? payload.vendorId[0] : payload.vendorId;
    data.append("vendorId", vendorId?.toString() || "");
    data.append("branchId", payload.branchId || "");
    data.append("expiresAt", payload.expiresAt ? payload.expiresAt : "");
    data.append("serviceId", payload.serviceId);
    data.append("productCategoryId", payload.productCategoryId);

    // Add fulfillment settings
    if (payload.fulfillmentSettings) {
      const archetype = archetypeMap[payload.type];
      if (archetype) {
        data.append("archetype", archetype);
        
        // Add only relevant settings for this archetype
        const relevantFields = relevantSettings[archetype];
        relevantFields.forEach(field => {
          const value = payload.fulfillmentSettings?.[field];
          if (value !== undefined && value !== null) {
            data.append(field, value.toString());
          }
        });
      }
    }

    if (payload.tagIds) {
      payload.tagIds.forEach((id, i) => {
        data.append(`tagIds[${i}]`, id.toString());
      });
    }

    // Add modifier options data
    if (payload.available_modifiers) {
      Object.entries(payload.available_modifiers).forEach(([modifierId, settings]) => {
        data.append(`available_modifiers[${modifierId}][price_adjustment_override]`,
          settings.price_adjustment_override?.toString() || "0");
        data.append(`available_modifiers[${modifierId}][is_default]`,
          settings.is_default?.toString() || "false");
        data.append(`available_modifiers[${modifierId}][sort_order]`,
          settings.sort_order?.toString() || "0");
      });
    }

    // Add packaging options data
    if (payload.packagingOptionIds && payload.packagingOptionIds.length > 0) {
      payload.packagingOptionIds.forEach((optionId) => {
        data.append("packagingOptionIds[]", optionId.toString());
      });
    }

    // if (payload.accompaniments) {
    //   Object.keys(payload.accompaniments).forEach((i) => {
    //     // Ensure ID is correctly formatted (if it should be a string or integer)
    //     data.append(`accompaniments[${i}][id]`, String(payload.accompaniments[i]["id"]));
    //     data.append(
    //       `accompaniments[${i}][price]`,
    //       String(payload.accompaniments[i]["price"]),
    //     );
    //   });
    // }
    
    // if (payload.upsells) {
    //   Object.keys(payload.upsells).forEach((i) => {
    //     data.append(`upsells[${i}][id]`, payload.upsells[i]["id"]);
    //     data.append(
    //       `upsells[${i}][price]`,
    //       String(payload.upsells[i]["price"]),
    //     );
    //   });
    // }

    if (payload.imageUpload) {
      data.append("image", payload.imageUpload);
    }

    if (payload.galleryUpload) {
      Array.from(payload.galleryUpload).forEach((i) => {
        data.append("gallery[]", i);
      });
    }

    toast.promise(storeProduct(data), {
      loading: "Saving product...",
      success: "Product updated successfully 👌",
      error: "Failed to update product 🤯",
    });
  };

  return (
    <form
      className="bg-white p-6 rounded-lg"
      encType="multipart/form-data"
      onSubmit={handleSubmit(onSubmit)}
    >
      <div className="mb-6 flex w-full items-center justify-between">
        <h1 className="text-2xl font-bold">{steps[step]}</h1>
        <nav
          className="flex flex-wrap justify-end gap-4"
          aria-label="Tabs"
          role="tablist"
        >
          {Object.keys(steps).map((key) => (
            <button
              key={key}
              type="button"
              className={
                "active hs-tab-active:bg-primary hs-tab-active:text-white flex w-full justify-center rounded-lg px-10 py-3 text-center text-sm font-medium text-primary sm:w-auto " +
                (step === key
                  ? "bg-primary text-white"
                  : "bg-primary/10 text-default-900")
              }
              data-hs-tab="#tabPersonalDetail"
              aria-controls="tabPersonalDetail"
              role="tab"
              aria-selected="true"
              onClick={() => setStep(key)}
            >
              {steps[key]}
            </button>
          ))}
        </nav>
      </div>

      {step === "images" && (
        <div className="grid grid-cols-2 gap-5">
          <label
            className={
              "mb-4 flex flex-col items-center justify-center " +
              (imagePreview
                ? ""
                : "h-96 rounded-lg border border-dashed border-default-500")
            }
          >
            {imagePreview ? (
              <Image
                src={imagePreview}
                alt="Product Image"
                width={500}
                height={500}
                className="w-full object-contain"
              />
            ) : (
              "Upload image"
            )}

            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
          </label>

          <div>
            <h4 className="mb-4 text-base font-medium text-default-800">
              Additional Images
            </h4>

            <div className="grid grid-cols-3 gap-4">
              {galleryPreview.map((image, i) => (
                <Image
                  key={i}
                  src={image}
                  alt="Product Image"
                  width={300}
                  height={300}
                  className="h-40 w-full object-contain"
                />
              ))}
              <label className="flex h-40 flex-col items-center justify-center rounded-lg border border-default-200 p-6">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleGalleryUpload}
                  className="hidden"
                />
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-9 w-9"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                  />
                </svg>
              </label>
            </div>
          </div>

          <div className="col-span-2 flex items-center justify-between pt-20">
            <button
              className="rounded-lg border border-primary px-6 py-2 text-primary"
              onClick={() => setStep("fulfillment")}
            >
              Previous
            </button>

            <button
              className="rounded-lg bg-primary px-6 py-2 text-white"
              type="button"
              onClick={() => setStep("extra")}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {step === "details" && (
        <div className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-2">
            <div className="space-y-6">
              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="productname"
                >
                  {product.type === "Service" ? "Service" : "Product"} Name
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="Enter name"
                    {...register("name", {
                      required: true,
                    })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>

              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="productname"
                >
                  {product.type === "Service" ? "Service" : "Product"} Reference
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="Enter reference number"
                    {...register("ref", {
                      required: true,
                    })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>

              {product.payment !== "Free" && (
                <div className="grid gap-6 lg:grid-cols-2">
                  <div className="relative max-w-full">
                    <label
                      className="mb-2 block text-sm font-medium text-default-900"
                      htmlFor="sellingPrice"
                    >
                      Selling Price
                    </label>
                    <div className="relative max-w-full">
                      <input
                        type="number"
                        placeholder="Selling Price"
                        {...register("price", {
                          required: true,
                        })}
                        className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                      />
                    </div>
                  </div>
                  <div className="relative max-w-full">
                    <label
                      className="mb-2 block text-sm font-medium text-default-900"
                      htmlFor="costPrice"
                    >
                      Discounted Price
                    </label>
                    <div className="relative max-w-full">
                      <input
                        type="number"
                        placeholder="Discounted Price"
                        {...register("discounted", {
                          required: true,
                        })}
                        className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                      />
                    </div>
                  </div>
                </div>
              )}

              {product.discounted! > 0 && (
                <div className="grid gap-6 lg:grid-cols-2">
                  <div className="relative w-full">
                    <label
                      className="mb-2 block text-sm font-medium text-default-900"
                      htmlFor="saleStartDate"
                    >
                      Sale Start On
                    </label>
                    <div className="relative w-full">
                      <CustomDatePicker
                        value={product.saleStartDate ? new Date(product.saleStartDate) : null}
                        onChange={(date) => {
                          if (date) {
                            setValue("saleStartDate", date.toISOString());
                          } else {
                            setValue("saleStartDate", "");
                          }
                        }}
                      />
                    </div>
                  </div>
                  <div className="relative w-full">
                    <label
                      className="mb-2 block text-sm font-medium text-default-900"
                      htmlFor="saleEndDate"
                    >
                      Sale End On
                    </label>
                    <div className="relative w-full">
                      <CustomDatePicker
                        value={product.saleEndDate ? new Date(product.saleEndDate) : null}
                        onChange={(date) => {
                          if (date) {
                            setValue("saleEndDate", date.toISOString());
                          } else {
                            setValue("saleEndDate", "");
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="relative max-w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="quantity"
                >
                  Quantity (-1 for unlimited stock)
                </label>
                <div className="relative max-w-full">
                  <input
                    type="text"
                    placeholder="Quantity in Stock"
                    {...register("stock", {
                      required: true,
                    })}
                    className="form-input w-full rounded-lg border border-default-200 px-4 py-3 dark:bg-default-50"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                <div>
                  <label
                    htmlFor="payment"
                    className="mb-2 block text-sm font-medium text-default-900"
                  >
                    Payment
                  </label>
                  <div className="relative">
                    <div
                      className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                      id="payment"
                    >
                      <Select
                        options={[
                          {
                            value: "Free",
                            label: "Free",
                          },
                          {
                            value: "Prepaid",
                            label: "Prepaid",
                          },
                          {
                            value: "Postpaid",
                            label: "Postpaid",
                          },
                        ]}
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        defaultValue={{
                          value: product.payment,
                          label: product.payment,
                        }}
                        onChange={(option) => {
                          setValue("payment", option?.value!);
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="visibility"
                    className="mb-2 block text-sm font-medium text-default-900"
                  >
                    Visibility
                  </label>
                  <div className="relative">
                    <div
                      className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                      id="visibility"
                    >
                      <Select
                        options={[
                          {
                            value: "Public",
                            label: "Public",
                          },
                          {
                            value: "Private",
                            label: "Private",
                          },
                          {
                            value: "Restricted",
                            label: "Restricted",
                          },
                        ]}
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        defaultValue={{
                          value: product.visibility,
                          label: product.visibility,
                        }}
                        onChange={(option) => {
                          setValue(
                            "visibility",
                            // @ts-ignore
                            option?.value!,
                          );
                        }}
                      />
                    </div>
                  </div>
                </div>

                {!branch && !product.branchId && (
                  <>
                    <div className="space-y-2">
                      <label className="mb-2 block text-sm font-medium text-default-900">
                        Vendor
                      </label>

                      {userDetails?.roles?.some((role) => role.name === 'vendor') ? (
                        <>
                          <input
                            type="text"
                            value={userDetails?.name || 'Unknown Vendor'}
                            readOnly
                            className="form-input rounded-lg border-default-200 px-4 py-2 text-default-900 dark:bg-default-50 w-full"
                          />
                          <input
                            type="hidden"
                            {...register("vendorId")}
                            value={userDetails?.id}
                          />
                        </>
                      ) : (
                      <Controller
                        name="vendorId"
                        control={control}
                        render={({ field }) => (
                          <AsyncSelect
                            isClearable
                            isSearchable
                            loadOptions={loadVendorOptions}
                            onChange={(option) => {
                                setValue("vendorId", option?.value || "");
                            }}
                            placeholder="Search vendor by name"
                            classNames={{
                              control: () =>
                                "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                              input: () => "border-none",
                              container: () =>
                                "!border-none border-initial !border",
                            }}
                              components={{
                                IndicatorSeparator: () => null,
                              }}
                          />
                        )}
                      />
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="mb-2 block text-sm font-medium text-default-900">
                        Branch
                      </label>
                      <Controller
                        name="branchId"
                        control={control}
                        render={({ field }) => (
                          <AsyncSelect
                            isClearable
                            // cacheOptions
                            isSearchable
                            loadOptions={s => loadBranchOptions(s, product.vendorId)}
                            onChange={(option) => {
                              setValue("branchId", option?.value!);
                            }}
                            placeholder="Search branch by name"
                            classNames={{
                              control: () =>
                                "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                              input: () => "border-none",
                              container: () =>
                                "!border-none border-initial !border",
                            }}
                          />
                        )}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <label className="mb-2 block text-sm font-medium text-default-900">
                  Status
                </label>

                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      options={[
                        {
                          value: "Draft",
                          label: "Draft",
                        },
                        {
                          value: "Pending",
                          label: "Pending",
                        },
                        {
                          value: "Published",
                          label: "Published",
                        },
                        {
                          value: "Unpublished",
                          label: "Unpublished",
                        },
                        {
                          value: "Archived",
                          label: "Archived",
                        },
                      ]}
                      defaultValue={{
                        value: product.status,
                        label: product.status,
                      }}
                      onChange={(option) =>
                        setValue(
                          "status",
                          // @ts-ignore
                          option?.value!,
                        )
                      }
                      classNames={{
                        control: () =>
                          "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                        input: () => "border-none",
                        container: () => "!border-none border-initial !border",
                      }}
                    />
                  )}
                />
              </div>

              {product.type !== "Service" && (
                <div>
                  <label
                    htmlFor="availability"
                    className="mb-2 block text-sm font-medium text-default-900"
                  >
                    Availability
                  </label>
                  <div className="relative">
                    <div
                      className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                      id="availability"
                    >
                      <Select
                        options={[
                          {
                            value: "In Stock",
                            label: "In Stock",
                          },
                          {
                            value: "Out of Stock",
                            label: "Out of Stock",
                          },
                          {
                            value: "Pre Order",
                            label: "Pre Order",
                          },
                        ]}
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        defaultValue={{
                          value: product.availability,
                          label: product.availability,
                        }}
                        onChange={(option) => {
                          setValue(
                            "availability",
                            // @ts-ignore
                            option?.value!,
                          );
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}

              {product.type === "Physical" && (
                <div>
                  <label
                    htmlFor="shipping"
                    className="mb-2 block text-sm font-medium text-default-900"
                  >
                    Shipping
                  </label>
                  <div className="relative">
                    <div
                      className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50"
                      id="shipping"
                    >
                      <Select
                        options={[
                          {
                            value: "Free",
                            label: "Free",
                          },
                          {
                            value: "Paid",
                            label: "Paid",
                          },
                          {
                            value: "Pickup",
                            label: "Pickup",
                          },
                        ]}
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        defaultValue={{
                          value: product.shipping,
                          label: product.shipping,
                        }}
                        onChange={(option) => {
                          setValue(
                            "shipping",
                            // @ts-ignore
                            option?.value!,
                          );
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="mb-5 w-full">
                <label
                  className="mb-2 block text-sm font-medium text-default-900"
                  htmlFor="editor"
                >
                  {product.type === "Service" ? "Service" : "Product"}{" "}
                  description
                </label>

                <ReactQuill
                  className="block h-60 rounded-lg [&>.ql-container]:!rounded-b-lg [&>.ql-container]:!border-default-200 [&>.ql-toolbar]:!rounded-t-lg [&>.ql-toolbar]:!border-default-200"
                  id="editor"
                  theme="snow"
                  value={product.details}
                  onChange={(value) => setValue("details", value)}
                  placeholder="Describe product"
                />
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end pt-20">
            <button
              className="rounded-lg bg-primary px-6 py-2 text-white"
              type="button"
              onClick={() => setStep("fulfillment")}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {step === "fulfillment" && (
        <div className="space-y-4">
          <FulfillmentSettings 
            productType={product.type} 
            register={register}
            watch={watch}
            setValue={setValue}
            errors={errors}
          />

          <div className="flex items-center justify-between pt-20">
            <button
              className="rounded-lg border border-primary px-6 py-2 text-primary"
              onClick={() => setStep("details")}
            >
              Previous
            </button>

            <button
              className="rounded-lg bg-primary px-6 py-2 text-white"
              type="button"
              onClick={() => setStep("images")}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {step === "extra" && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-5">
            <div className="space-y-4">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="accompaniments"
              >
                Select accompaniments (user selects one or more)
              </label>

              <div className="relative">
                <div className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50">
                  <Controller
                    name="accompaniments"
                    control={control}
                    render={({ field }) => (
                      <AsyncSelect
                        id="accompaniments"
                        isMulti
                        isClearable
                        // cacheOptions
                        isSearchable
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        loadOptions={loadProducts}
                        placeholder="Search product by name"
                        getOptionLabel={(t: Product) => t.name}
                        getOptionValue={(t: Product) => t.id}
                        onChange={(products) =>
                          setValue(
                            "accompaniments",
                            products?.reduce(
                              (acc, option: Product) => ({
                                ...acc,
                                [option.id]: {
                                  id: option.id,
                                  price: option.price,
                                  name: option.name,
                                },
                              }),
                              {},
                            ),
                          )
                        }
                      />
                    )}
                  />
                </div>

                {Object.entries(product.accompaniments).length > 0 && (
                  <table className="mt-5 table w-full rounded-lg border border-primary">
                    <thead>
                      <tr className="space-x-5">
                        <th className="px-4 py-2 text-left">Name</th>
                        <th className="px-4 py-2 text-left">Price</th>
                        <th className="py-2 text-left">Action</th>
                      </tr>
                    </thead>

                    <tbody>
                      {Object.entries(product.accompaniments).map(
                        ([key, value]) => (
                          <tr key={key}>
                            <td className="p-4">{value.name}</td>
                            <td className="p-4">
                              <input
                                type="number"
                                step={0.01}
                                min={0}
                                defaultValue={value.price}
                                onChange={({ target }) => {
                                  setValue(
                                    `accompaniments.${key}.price`,
                                    parseFloat(target.value),
                                  );
                                }}
                                className="rounded-lg border border-primary px-4 py-2"
                              />
                            </td>

                            <td>
                              <button
                                className="text-red-500"
                                onClick={() => {
                                  const acc = product.accompaniments;

                                  delete acc[key];

                                  setValue("accompaniments", acc);
                                }}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  strokeWidth={1.5}
                                  stroke="currentColor"
                                  className="h-6 w-6"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                  />
                                </svg>
                              </button>
                            </td>
                          </tr>
                        ),
                      )}
                    </tbody>
                  </table>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <label
                className="mb-2 block text-sm font-medium text-default-900"
                htmlFor="upsells"
              >
                Select upsells (user selects one or more)
              </label>

              <div className="relative">
                <div className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50">
                  <Controller
                    name="upsells"
                    control={control}
                    render={({ field }) => (
                      <AsyncSelect
                        id="upsells"
                        isMulti
                        isClearable
                        // cacheOptions
                        isSearchable
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        loadOptions={loadProducts}
                        placeholder="Search product by name"
                        getOptionLabel={(t: Product) => t.name}
                        getOptionValue={(t: Product) => t.id}
                        onChange={(products) =>
                          setValue(
                            "upsells",
                            products?.reduce(
                              (acc, option: Product) => ({
                                ...acc,
                                [option.id]: {
                                  id: option.id,
                                  price: option.price,
                                  name: option.name,
                                },
                              }),
                              {},
                            ),
                          )
                        }
                      />
                    )}
                  />
                </div>

                {Object.entries(product.upsells).length > 0 && (
                  <table className="mt-5 table w-full rounded-lg border border-primary">
                    <thead>
                      <tr className="space-x-5">
                        <th className="px-4 py-2 text-left">Name</th>
                        <th className="px-4 py-2 text-left">Price</th>
                        <th className="py-2 text-left">Action</th>
                      </tr>
                    </thead>

                    <tbody>
                      {Object.entries(product.upsells).map(([key, value]) => (
                        <tr key={key}>
                          <td className="p-4">{value.name}</td>
                          <td className="p-4">
                            <input
                              type="number"
                              step={0.01}
                              min={0}
                              defaultValue={value.price}
                              onChange={({ target }) => {
                                setValue(
                                  `upsells.${key}.price`,
                                  parseFloat(target.value),
                                );
                              }}
                              className="rounded-lg border border-primary px-4 py-2"
                            />
                          </td>

                          <td>
                            <button
                              className="text-red-500"
                              onClick={() => {
                                const acc = product.upsells;

                                delete acc[key];

                                setValue("upsells", acc);
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth={1.5}
                                stroke="currentColor"
                                className="h-6 w-6"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                              </svg>
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </div>
            </div>
            <div className="col-span-2 space-y-4">
              <label
                htmlFor="tags"
                className="mb-2 block text-sm font-medium text-default-900"
              >
                {product.type === "Service" ? "Service" : "Product"} tags
              </label>

              <div className="relative">
                <div className="css-b62m3t-container block w-full cursor-pointer rounded-lg border border-default-200 bg-transparent focus-within:border focus-within:border-primary dark:bg-default-50">
                  <Controller
                    name="tagIds"
                    control={control}
                    render={({ field }) => (
                      <AsyncCreatableSelect
                        id="tags"
                        isClearable
                        isLoading={isLoadingTags}
                        isSearchable
                        isMulti
                        classNames={{
                          control: () =>
                            "form-input !rounded-lg !border-default-200 px-4 py-1 dark:bg-default-50 w-full",
                          input: () => "border-none",
                          container: () =>
                            "!border-none border-initial !border",
                        }}
                        loadOptions={loadTags}
                        getOptionLabel={(c: Tag) => c.name}
                        getOptionValue={(c: Tag) => c?.id?.toString()}
                        onChange={(option) =>
                          setValue(
                            "tagIds",
                            option.map((t) => t.id),
                          )
                        }
                        placeholder="Search by name"
                        formatCreateLabel={() => `Create new tag`}
                        onCreateOption={createTag}
                        defaultValue={product.tags}
                      />
                    )}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <label className="mb-2 block text-sm font-medium text-default-900">
              Variation
            </label>

            <div className="flex justify-between pr-6">
              {["Single", "Variable"].map((m) => (
                <div className="flex items-center gap-4" key={m}>
                  <input
                    type="radio"
                    id={m}
                    value={m}
                    {...register("mode")}
                    className="relative h-7 w-[3.25rem] cursor-pointer appearance-none rounded-full border-2 border-transparent bg-default-200 transition-colors duration-200 ease-in-out before:inline-block before:h-6 before:w-6 before:translate-x-0 before:transform before:rounded-full before:bg-white before:shadow before:transition before:duration-200 before:ease-in-out checked:!bg-primary checked:bg-none checked:before:translate-x-full focus:ring-0 focus:ring-transparent"
                  />
                  <label className="block text-sm text-default-600" htmlFor={m}>
                    {m}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-between">
            <h4 className="text-sm font-medium text-default-600">
              Expiry Date
            </h4>
            <div className="flex items-center gap-4">
              <label
                className="block text-sm text-default-600"
                htmlFor="addExpiryDate"
              >
                Add Expiry Date
              </label>
              <input
                type="checkbox"
                id="addExpiryDate"
                {...register("hasExpiry")}
                className="relative h-7 w-[3.25rem] cursor-pointer appearance-none rounded-full border-2 border-transparent bg-default-200 transition-colors duration-200 ease-in-out before:inline-block before:h-6 before:w-6 before:translate-x-0 before:transform before:rounded-full before:bg-white before:shadow before:transition before:duration-200 before:ease-in-out checked:!bg-primary checked:bg-none checked:before:translate-x-full focus:ring-0 focus:ring-transparent"
              />
            </div>
          </div>

          {product.hasExpiry && (
            <CustomDatePicker
              value={product.expiresAt ? new Date(product.expiresAt) : null}
              onChange={(date) => {
                if (date) {
                  setValue("expiresAt", date.toISOString());
                } else {
                  setValue("expiresAt", "");
                }
              }}
            />
          )}

          <div className="mt-5 block">
            <div className="flex justify-between">
              <h4 className="text-sm font-medium text-default-600">
                Return Policy
              </h4>

              <div className="flex items-center gap-4">
                <label
                  className="block text-sm text-default-600"
                  htmlFor="returnPolicy"
                >
                  Returnable
                </label>
                <input
                  type="checkbox"
                  id="returnPolicy"
                  className="relative h-7 w-[3.25rem] cursor-pointer appearance-none rounded-full border-2 border-transparent bg-default-200 transition-colors duration-200 ease-in-out before:inline-block before:h-6 before:w-6 before:translate-x-0 before:transform before:rounded-full before:bg-white before:shadow before:transition before:duration-200 before:ease-in-out checked:!bg-primary checked:bg-none checked:before:translate-x-full focus:ring-0 focus:ring-transparent"
                />
              </div>
            </div>
          </div>

          <ProductModifiers
            control={control}
            setValue={setValue}
            watch={watch}
            vendorId={product.vendorId}
            loadModifiers={loadModifiers}
          />

          <PackagingOptionSelector
            control={control}
            setValue={setValue}
            watch={watch}
            vendorId={product.vendorId}
            loadPackagingOptions={loadPackagingOptions}
          />

          <div className="flex items-center justify-between pt-20">
            <button
              className="rounded-lg border border-primary px-6 py-2 text-primary"
              onClick={() => setStep("images")}
            >
              Previous
            </button>

            <button
              className="rounded-lg bg-primary px-6 py-2 text-white"
              type="button"
              onClick={() => setStep("review")}
            >
              Next
            </button>
          </div>
        </div>
      )}

      {step === "review" && (
        <div className="space-y-4">
          <ProductPreview product={product} branch={branch} />

          <div className="col-span-2 flex flex-wrap items-center justify-end gap-4">
            <div className="flex flex-wrap items-center gap-4">
              <button
                type="reset"
                className="flex items-center justify-center gap-2 rounded-lg bg-red-500/10 px-6 py-3 text-center text-sm font-semibold text-red-500 shadow-sm transition-colors duration-200 hover:bg-red-500 hover:text-white"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m7 21-4.3-4.3c-1-1-1-2.5 0-3.4l9.6-9.6c1-1 2.5-1 3.4 0l5.6 5.6c1 1 1 2.5 0 3.4L13 21"></path>
                  <path d="M22 21H7"></path>
                  <path d="m5 11 9 9"></path>
                </svg>
                Clear
              </button>
              <button
                type="submit"
                className="flex items-center justify-center gap-2 rounded-lg bg-primary px-6 py-3 text-center text-sm font-semibold text-white shadow-sm transition-all duration-200 hover:bg-default-500"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  height="20"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
				Update
              </button>
            </div>
          </div>
        </div>
      )}
    </form>
  );
}
