import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect, notFound } from "next/navigation";
import ServiceAreaForm from "@/components/forms/service-area-form";
import { revalidatePath } from "next/cache";
import { PaginatedData, Branch, ServiceArea } from "@/types";
import { Button } from "@nextui-org/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

const fetchServiceArea = cache(
  (vendorId: string, serviceAreaId: string) =>
    api.get<ServiceArea>(`vendors/${vendorId}/service-areas/${serviceAreaId}`),
);

const fetchBranches = cache(
  (vendorId: string) =>
    api.get<PaginatedData<Branch>>(`vendors/${vendorId}/branches`),
);

export const generateMetadata = async ({
  params,
}: {
  params: { serviceAreaId: string };
}) => {
  return {
    title: "Edit Service Area",
    description: "Edit service area details and coverage settings",
    keywords: ["service area", "delivery", "coverage", "edit"],
  };
};

export default async function EditServiceAreaPage({
  params: { serviceAreaId },
}: {
  params: { serviceAreaId: string };
}) {
  const session = await auth();

  // Check if user is authenticated and has vendor access
  if (!session) {
    redirect('/login');
  }

  // Get vendor ID from session
  const vendorId = session?.vendor?.id || session?.branch?.vendorId;
  
  if (!vendorId) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No Vendor Access
          </h2>
          <p className="text-gray-600">
            You need to be associated with a vendor to edit service areas.
          </p>
        </div>
      </div>
    );
  }

  // Fetch service area and branches
  const [serviceArea, branches] = await Promise.all([
    fetchServiceArea(vendorId, serviceAreaId),
    fetchBranches(vendorId),
  ]);

  if (!serviceArea) {
    notFound();
  }

  const updateServiceArea = async (data: FormData) => {
    "use server";

    try {
      await api.put(`vendors/${vendorId}/service-areas/${serviceAreaId}`, data);
      revalidatePath("/service-areas");
      revalidatePath(`/service-areas/${serviceAreaId}`);
      redirect(`/service-areas/${serviceAreaId}`);
    } catch (error) {
      console.error("Failed to update service area:", error);
      throw error;
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <Button
          as={Link}
          href={`/service-areas/${serviceAreaId}`}
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
          className="mb-4"
        >
          Back to Service Area
        </Button>
        
        <h1 className="text-2xl font-bold text-gray-900">Edit Service Area</h1>
        <p className="text-gray-600 mt-1">
          Update the details and coverage settings for "{serviceArea.name}"
        </p>
      </div>

      <div className="max-w-4xl">
        <ServiceAreaForm
          action={updateServiceArea}
          branches={branches?.data || []}
          vendorId={vendorId}
          serviceArea={serviceArea}
          isEdit={true}
        />
      </div>
    </div>
  );
}
