import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import AdminServiceAreasTable from "@/components/admin/delivery/admin-service-areas-table";
import { PaginatedData, ServiceArea } from "@/types";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface AdminServiceArea extends Omit<ServiceArea, 'vendor'> {
  vendor: {
    id: string;
    name: string;
    verification_status: string;
  };
  conflicts?: Array<{
    id: string;
    type: 'overlap' | 'priority_conflict';
    conflicting_area: {
      id: string;
      name: string;
      vendor_name: string;
    };
    severity: 'low' | 'medium' | 'high';
  }>;
  performance_metrics?: {
    order_count: number;
    success_rate: number;
    average_delivery_time: number;
  };
}

const fetchAdminServiceAreas = cache(
  (searchParams?: Record<string, string>) =>
    api.get<PaginatedData<AdminServiceArea>>(
      'admin/delivery-management/service-areas',
      searchParams,
    ),
);

export const generateMetadata = async () => {
  return {
    title: "Service Areas Management",
    description: "System-wide service area management and conflict resolution",
    keywords: ["admin", "delivery", "service areas", "coverage"],
  };
};

export default async function AdminServiceAreasPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch service areas data
  const serviceAreas = await fetchAdminServiceAreas(searchParams).catch(() => null);

  // Demo data fallback
  const demoServiceAreas: PaginatedData<AdminServiceArea> = {
    data: [
      {
        id: "sa1",
        vendor_id: "v1",
        branch_id: "b1",
        name: "Downtown Circle",
        description: "Central business district coverage",
        type: "circle",
        radius: 5,
        priority: 1,
        active: true,
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        vendor: {
          id: "v1",
          name: "FastDelivery Co.",
          verification_status: "verified",
        },
        branch: {
          id: "b1",
          name: "Main Branch",
          details: "123 Main St",
          phone: "+254700123456",
          email: "<EMAIL>",
          image: null,
          cover: null,
          vendorId: "v1",
          location: null,
          geom: null,
          hours: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          vendor: {} as any,
          sections: [],
          staff: [],
          sectionCount: 0,
          staffCount: 0,
          orderCount: 0,
        },
        conflicts: [
          {
            id: "c1",
            type: "overlap",
            conflicting_area: {
              id: "sa2",
              name: "CBD Coverage",
              vendor_name: "QuickCourier",
            },
            severity: "medium",
          },
        ],
        performance_metrics: {
          order_count: 1250,
          success_rate: 96.5,
          average_delivery_time: 25,
        },
      },
      {
        id: "sa2",
        vendor_id: "v2",
        branch_id: "b2",
        name: "CBD Coverage",
        description: "Central business district delivery zone",
        type: "circle",
        radius: 4,
        priority: 2,
        active: true,
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(),
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(),
        vendor: {
          id: "v2",
          name: "QuickCourier",
          verification_status: "verified",
        },
        branch: {
          id: "b2",
          name: "CBD Branch",
          details: "456 CBD Ave",
          phone: "+254700234567",
          email: "<EMAIL>",
          image: null,
          cover: null,
          vendorId: "v2",
          location: null,
          geom: null,
          hours: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          vendor: {} as any,
          sections: [],
          staff: [],
          sectionCount: 0,
          staffCount: 0,
          orderCount: 0,
        },
        conflicts: [
          {
            id: "c2",
            type: "overlap",
            conflicting_area: {
              id: "sa1",
              name: "Downtown Circle",
              vendor_name: "FastDelivery Co.",
            },
            severity: "medium",
          },
        ],
        performance_metrics: {
          order_count: 890,
          success_rate: 92.1,
          average_delivery_time: 32,
        },
      },
      {
        id: "sa3",
        vendor_id: "v3",
        branch_id: "b3",
        name: "Westlands Zone",
        description: "Westlands shopping and business area",
        type: "circle",
        radius: 3,
        priority: 1,
        active: true,
        created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(),
        updated_at: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(),
        vendor: {
          id: "v3",
          name: "SpeedyService",
          verification_status: "pending",
        },
        branch: {
          id: "b3",
          name: "Westlands Branch",
          details: "789 Westlands Rd",
          phone: "+254700345678",
          email: "<EMAIL>",
          image: null,
          cover: null,
          vendorId: "v3",
          location: null,
          geom: null,
          hours: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          vendor: {} as any,
          sections: [],
          staff: [],
          sectionCount: 0,
          staffCount: 0,
          orderCount: 0,
        },
        performance_metrics: {
          order_count: 234,
          success_rate: 78.5,
          average_delivery_time: 45,
        },
      },
    ],
    meta: {
      current_page: 1,
      last_page: 1,
      per_page: 10,
      total: 3,
      from: 1,
      to: 3,
    },
  };

  const resolveConflict = async (data: FormData) => {
    "use server";

    const conflictId = data.get("conflictId") as string;
    const resolution = data.get("resolution") as string;

    try {
      await api.post(`admin/delivery-management/service-areas/conflicts/${conflictId}/resolve`, {
        resolution,
      });
      revalidatePath("/admin/delivery-management/service-areas");
    } catch (error) {
      console.error("Failed to resolve conflict:", error);
      throw error;
    }
  };

  const updateServiceAreaStatus = async (data: FormData) => {
    "use server";

    const serviceAreaId = data.get("serviceAreaId") as string;
    const active = data.get("active") === "true";

    try {
      await api.patch(`admin/delivery-management/service-areas/${serviceAreaId}`, {
        active,
      });
      revalidatePath("/admin/delivery-management/service-areas");
    } catch (error) {
      console.error("Failed to update service area status:", error);
      throw error;
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Service Areas Management</h1>
        <p className="text-gray-600 mt-1">
          System-wide service area oversight, conflict detection, and coverage analysis
        </p>
      </div>

      <AdminServiceAreasTable
        data={serviceAreas?.data || demoServiceAreas.data}
        meta={serviceAreas?.meta || demoServiceAreas.meta}
        resolveConflict={resolveConflict}
        updateServiceAreaStatus={updateServiceAreaStatus}
      />
    </div>
  );
}
