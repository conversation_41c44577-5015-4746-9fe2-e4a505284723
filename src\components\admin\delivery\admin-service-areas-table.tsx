"use client";

import React, { useState } from "react";
import { CustomTable } from "../../custom-table";
import Link from "next/link";
import { 
  <PERSON>, 
  Button, 
  Switch,
  <PERSON>dal,
  <PERSON>dalContent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dalBody,
  ModalFooter,
  useDisclosure,
  Card,
  CardBody,
  CardHeader,
} from "@nextui-org/react";
import { Icon } from "../../icon";
import { 
  Eye, 
  AlertTriangle, 
  MapPin, 
  TrendingUp, 
  TrendingDown,
  CheckCircle,
  Clock,
} from "lucide-react";
import toast from "react-hot-toast";
import { PaginationMeta, ServiceArea } from "@/types";

interface AdminServiceArea extends ServiceArea {
  vendor: {
    id: string;
    name: string;
    verification_status: string;
  };
  conflicts?: Array<{
    id: string;
    type: 'overlap' | 'priority_conflict';
    conflicting_area: {
      id: string;
      name: string;
      vendor_name: string;
    };
    severity: 'low' | 'medium' | 'high';
  }>;
  performance_metrics?: {
    order_count: number;
    success_rate: number;
    average_delivery_time: number;
  };
}

interface AdminServiceAreasTableProps {
  data: AdminServiceArea[];
  meta: PaginationMeta;
  resolveConflict: (data: FormData) => Promise<void>;
  updateServiceAreaStatus: (data: FormData) => Promise<void>;
}

export default function AdminServiceAreasTable({
  data,
  meta,
  resolveConflict,
  updateServiceAreaStatus,
}: AdminServiceAreasTableProps) {
  const [selectedArea, setSelectedArea] = useState<AdminServiceArea | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const onToggleActive = async (serviceAreaId: string, currentActive: boolean) => {
    const formData = new FormData();
    formData.append("serviceAreaId", serviceAreaId);
    formData.append("active", (!currentActive).toString());

    toast.promise(updateServiceAreaStatus(formData), {
      loading: `${currentActive ? 'Deactivating' : 'Activating'} service area...`,
      success: `Service area ${currentActive ? 'deactivated' : 'activated'}!`,
      error: `Failed to ${currentActive ? 'deactivate' : 'activate'} service area`,
    });
  };

  const handleResolveConflict = async (conflictId: string, resolution: string) => {
    const formData = new FormData();
    formData.append("conflictId", conflictId);
    formData.append("resolution", resolution);

    toast.promise(resolveConflict(formData), {
      loading: "Resolving conflict...",
      success: "Conflict resolved successfully!",
      error: "Failed to resolve conflict",
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'circle':
        return <Icon name="icon-[mingcute--location-line]" classNames="text-blue-500" />;
      case 'polygon':
        return <Icon name="icon-[mingcute--polygon-line]" classNames="text-green-500" />;
      case 'administrative':
        return <Icon name="icon-[mingcute--building-2-line]" classNames="text-purple-500" />;
      default:
        return <Icon name="icon-[mingcute--map-pin-line]" classNames="text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'circle':
        return 'primary';
      case 'polygon':
        return 'success';
      case 'administrative':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getConflictSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'danger';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const getVerificationStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'danger';
      default:
        return 'default';
    }
  };

  const formatCoverage = (serviceArea: AdminServiceArea) => {
    switch (serviceArea.type) {
      case 'circle':
        return `${serviceArea.radius}km radius`;
      case 'polygon':
        return 'Custom polygon';
      case 'administrative':
        return serviceArea.administrative_areas?.length 
          ? `${serviceArea.administrative_areas.length} area(s)`
          : 'Administrative areas';
      default:
        return 'Unknown';
    }
  };

  const openDetailsModal = (area: AdminServiceArea) => {
    setSelectedArea(area);
    onOpen();
  };

  return (
    <>
      <CustomTable
        title="System Service Areas"
        columns={[
          {
            name: "Service Area",
            uid: "name",
            sortable: true,
            renderCell: (area: AdminServiceArea) => (
              <div className="flex items-center space-x-3">
                {getTypeIcon(area.type)}
                <div>
                  <p className="font-semibold">{area.name}</p>
                  <p className="text-sm text-default-500">{area.vendor.name}</p>
                  {area.description && (
                    <p className="text-xs text-default-400">{area.description}</p>
                  )}
                </div>
              </div>
            ),
          },
          {
            name: "Vendor",
            uid: "vendor",
            sortable: true,
            renderCell: (area: AdminServiceArea) => (
              <div className="flex flex-col">
                <span className="font-medium">{area.vendor.name}</span>
                <Chip
                  size="sm"
                  variant="flat"
                  color={getVerificationStatusColor(area.vendor.verification_status) as any}
                  className="capitalize mt-1"
                >
                  {area.vendor.verification_status}
                </Chip>
              </div>
            ),
          },
          {
            name: "Type & Coverage",
            uid: "coverage",
            renderCell: (area: AdminServiceArea) => (
              <div className="flex flex-col">
                <Chip
                  size="sm"
                  variant="flat"
                  color={getTypeColor(area.type) as any}
                  className="capitalize mb-1"
                >
                  {area.type}
                </Chip>
                <span className="text-sm text-default-500">{formatCoverage(area)}</span>
              </div>
            ),
          },
          {
            name: "Performance",
            uid: "performance",
            renderCell: (area: AdminServiceArea) => {
              if (!area.performance_metrics) {
                return <span className="text-sm text-default-400">No data</span>;
              }
              
              return (
                <div className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-1">
                    <span className="text-sm font-medium">
                      {area.performance_metrics.success_rate}%
                    </span>
                    {area.performance_metrics.success_rate >= 95 ? (
                      <TrendingUp className="size-3 text-green-500" />
                    ) : (
                      <TrendingDown className="size-3 text-red-500" />
                    )}
                  </div>
                  <div className="text-xs text-default-500">
                    {area.performance_metrics.order_count} orders
                  </div>
                  <div className="text-xs text-default-500">
                    {area.performance_metrics.average_delivery_time}min avg
                  </div>
                </div>
              );
            },
          },
          {
            name: "Conflicts",
            uid: "conflicts",
            renderCell: (area: AdminServiceArea) => {
              if (!area.conflicts || area.conflicts.length === 0) {
                return (
                  <Chip size="sm" color="success" variant="flat">
                    No conflicts
                  </Chip>
                );
              }
              
              const highSeverityConflicts = area.conflicts.filter(c => c.severity === 'high').length;
              const mediumSeverityConflicts = area.conflicts.filter(c => c.severity === 'medium').length;
              
              return (
                <div className="flex flex-col space-y-1">
                  <Chip
                    size="sm"
                    color={highSeverityConflicts > 0 ? 'danger' : mediumSeverityConflicts > 0 ? 'warning' : 'success'}
                    variant="flat"
                    startContent={<AlertTriangle className="size-3" />}
                  >
                    {area.conflicts.length} conflict{area.conflicts.length > 1 ? 's' : ''}
                  </Chip>
                </div>
              );
            },
          },
          {
            name: "Priority",
            uid: "priority",
            sortable: true,
            renderCell: (area: AdminServiceArea) => (
              <Chip size="sm" variant="bordered">
                {area.priority || 0}
              </Chip>
            ),
          },
          {
            name: "Active",
            uid: "active",
            sortable: true,
            renderCell: (area: AdminServiceArea) => (
              <Switch
                size="sm"
                isSelected={area.active}
                onValueChange={(checked) => onToggleActive(area.id, area.active)}
                color="success"
              />
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (area: AdminServiceArea) => (
              <div className="flex w-fit items-center gap-3">
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={() => openDetailsModal(area)}
                >
                  <Eye className="size-4" />
                </Button>
                <Link href={`/admin/delivery-management/service-areas/${area.id}`}>
                  <Icon
                    name="icon-[mage--edit-pen-fill]"
                    classNames="text-primary hover:text-primary-600 cursor-pointer"
                  />
                </Link>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
        filter={{
          column: "type",
          displayName: "Type",
          values: [
            { name: "Circle", value: "circle" },
            { name: "Polygon", value: "polygon" },
            { name: "Administrative", value: "administrative" },
          ],
        }}
      />

      {/* Details Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            Service Area Details
          </ModalHeader>
          <ModalBody>
            {selectedArea && (
              <div className="space-y-4">
                {/* Basic Info */}
                <Card>
                  <CardHeader>
                    <h4 className="font-semibold">Basic Information</h4>
                  </CardHeader>
                  <CardBody>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Name</p>
                        <p className="font-medium">{selectedArea.name}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Vendor</p>
                        <p className="font-medium">{selectedArea.vendor.name}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Type</p>
                        <p className="font-medium capitalize">{selectedArea.type}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Coverage</p>
                        <p className="font-medium">{formatCoverage(selectedArea)}</p>
                      </div>
                    </div>
                  </CardBody>
                </Card>

                {/* Conflicts */}
                {selectedArea.conflicts && selectedArea.conflicts.length > 0 && (
                  <Card>
                    <CardHeader>
                      <h4 className="font-semibold">Conflicts</h4>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-3">
                        {selectedArea.conflicts.map((conflict) => (
                          <div key={conflict.id} className="p-3 border rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center space-x-2">
                                <Chip
                                  size="sm"
                                  color={getConflictSeverityColor(conflict.severity) as any}
                                  variant="flat"
                                >
                                  {conflict.severity} severity
                                </Chip>
                                <span className="text-sm capitalize">{conflict.type.replace('_', ' ')}</span>
                              </div>
                              <Button
                                size="sm"
                                color="primary"
                                variant="flat"
                                onPress={() => handleResolveConflict(conflict.id, 'auto_resolve')}
                              >
                                Resolve
                              </Button>
                            </div>
                            <p className="text-sm text-gray-600">
                              Conflicts with <strong>{conflict.conflicting_area.name}</strong> by{" "}
                              <strong>{conflict.conflicting_area.vendor_name}</strong>
                            </p>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>
                )}

                {/* Performance */}
                {selectedArea.performance_metrics && (
                  <Card>
                    <CardHeader>
                      <h4 className="font-semibold">Performance Metrics</h4>
                    </CardHeader>
                    <CardBody>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-primary">
                            {selectedArea.performance_metrics.order_count}
                          </p>
                          <p className="text-sm text-gray-600">Total Orders</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-success">
                            {selectedArea.performance_metrics.success_rate}%
                          </p>
                          <p className="text-sm text-gray-600">Success Rate</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-warning">
                            {selectedArea.performance_metrics.average_delivery_time}m
                          </p>
                          <p className="text-sm text-gray-600">Avg Delivery Time</p>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
