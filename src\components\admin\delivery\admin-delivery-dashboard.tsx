"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Clock, 
  MapPin, 
  CheckCircle, 
  AlertTriangle,
  Eye,
  FileText,
  Settings
} from "lucide-react";
import Link from "next/link";

interface DeliveryMetrics {
  active_vendors: number;
  pending_verifications: number;
  total_service_areas: number;
  coverage_regions: number;
  total_deliveries_today: number;
  success_rate: number;
  average_delivery_time: number;
  revenue_today: number;
}

interface RecentActivity {
  id: string;
  type: 'vendor_verified' | 'service_area_created' | 'verification_pending' | 'coverage_conflict';
  message: string;
  timestamp: string;
  vendor?: {
    id: string;
    name: string;
  };
}

interface AdminDeliveryDashboardProps {
  metrics: DeliveryMetrics;
  recentActivity: RecentActivity[];
}

export default function AdminDeliveryDashboard({
  metrics,
  recentActivity,
}: AdminDeliveryDashboardProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-K<PERSON>', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'vendor_verified':
        return <CheckCircle className="size-4 text-green-500" />;
      case 'service_area_created':
        return <MapPin className="size-4 text-blue-500" />;
      case 'verification_pending':
        return <Clock className="size-4 text-yellow-500" />;
      case 'coverage_conflict':
        return <AlertTriangle className="size-4 text-red-500" />;
      default:
        return <Clock className="size-4 text-gray-500" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'vendor_verified':
        return 'success';
      case 'service_area_created':
        return 'primary';
      case 'verification_pending':
        return 'warning';
      case 'coverage_conflict':
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <div className="space-y-6">
      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Vendors</p>
                <p className="text-2xl font-bold">{metrics.active_vendors.toLocaleString()}</p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="size-6 text-blue-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+12% from last month</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Reviews</p>
                <p className="text-2xl font-bold">{metrics.pending_verifications}</p>
              </div>
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="size-6 text-yellow-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <AlertTriangle className="size-4 text-yellow-500 mr-1" />
              <span className="text-sm text-yellow-600">Requires attention</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Service Areas</p>
                <p className="text-2xl font-bold">{metrics.total_service_areas.toLocaleString()}</p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <MapPin className="size-6 text-green-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+8% coverage expansion</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold">{metrics.success_rate}%</p>
              </div>
              <div className="p-2 bg-purple-100 rounded-lg">
                <CheckCircle className="size-6 text-purple-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+2.1% improvement</span>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* System Coverage Map Placeholder */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center space-x-2">
              <MapPin className="size-5" />
              <span>System Coverage Map</span>
            </h3>
          </CardHeader>
          <CardBody>
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="size-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 font-medium">Interactive Coverage Map</p>
                <p className="text-sm text-gray-500 mb-4">
                  System-wide delivery coverage visualization coming soon
                </p>
                <Button
                  as={Link}
                  href="/admin/delivery-management/service-areas"
                  color="primary"
                  variant="flat"
                  size="sm"
                >
                  View Service Areas
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Quick Actions</h3>
          </CardHeader>
          <CardBody className="space-y-3">
            <Button
              as={Link}
              href="/admin/delivery-management/verification"
              color="warning"
              variant="flat"
              className="w-full justify-start"
              startContent={<Clock className="size-4" />}
            >
              Review Pending ({metrics.pending_verifications})
            </Button>
            
            <Button
              as={Link}
              href="/admin/delivery-management/vendors"
              color="primary"
              variant="flat"
              className="w-full justify-start"
              startContent={<Users className="size-4" />}
            >
              Manage Vendors
            </Button>
            
            <Button
              as={Link}
              href="/admin/delivery-management/analytics"
              color="secondary"
              variant="flat"
              className="w-full justify-start"
              startContent={<FileText className="size-4" />}
            >
              Performance Report
            </Button>
            
            <Button
              as={Link}
              href="/admin/delivery-management/settings"
              color="default"
              variant="flat"
              className="w-full justify-start"
              startContent={<Settings className="size-4" />}
            >
              System Settings
            </Button>
          </CardBody>
        </Card>
      </div>

      {/* Performance Summary & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Summary */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Today's Performance</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Deliveries</span>
              <span className="font-semibold">{metrics.total_deliveries_today.toLocaleString()}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Average Delivery Time</span>
              <span className="font-semibold">{metrics.average_delivery_time} minutes</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Revenue Generated</span>
              <span className="font-semibold">{formatCurrency(metrics.revenue_today)}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Coverage Regions</span>
              <span className="font-semibold">{metrics.coverage_regions} regions</span>
            </div>
          </CardBody>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Recent Activity</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-3">
              {recentActivity.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50">
                  <div className="flex-shrink-0 mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">{formatTime(activity.timestamp)}</span>
                      {activity.vendor && (
                        <Chip
                          size="sm"
                          variant="flat"
                          color={getActivityColor(activity.type) as any}
                          className="text-xs"
                        >
                          {activity.vendor.name}
                        </Chip>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {recentActivity.length === 0 && (
                <div className="text-center py-4">
                  <p className="text-gray-500">No recent activity</p>
                </div>
              )}
              
              <div className="pt-2 border-t">
                <Button
                  as={Link}
                  href="/admin/delivery-management/activity"
                  variant="light"
                  size="sm"
                  className="w-full"
                  endContent={<Eye className="size-4" />}
                >
                  View All Activity
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
